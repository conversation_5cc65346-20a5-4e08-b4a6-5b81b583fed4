import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/category/dto/category_photo_list_interaction_data.dart';

final class CategoryPhotosInteractionNotifier
    extends AutoDisposeNotifier<Map<String, CategoryPhotoListInteractionData>> {
  @override
  Map<String, CategoryPhotoListInteractionData> build() {
    // ref.keepAlive();
    return {};
  }

  Map<String, CategoryPhotoListInteractionData> get data => state;

  CategoryPhotoListInteractionData getDataForCategory(String categorySlug) {
    return state[categorySlug] ?? CategoryPhotoListInteractionData();
  }

  int getLoadMoreLastId(String categorySlug) {
    return getDataForCategory(categorySlug).loadMoreLastId;
  }

  void setLoadMoreLastId(String categorySlug, int lastId) {
    final currentData = getDataForCategory(categorySlug);
    state = {
      ...state,
      categorySlug: currentData.copyWith(loadMoreLastId: lastId),
    };
  }

  void setLastItemSeenId(String categorySlug, int lastItemSeenId) {
    final currentData = getDataForCategory(categorySlug);
    state = {
      ...state,
      categorySlug: currentData.copyWith(lastItemSeenId: lastItemSeenId),
    };
  }

  void replace(String categorySlug, CategoryPhotoListInteractionData data) {
    state = {...state, categorySlug: data};
  }

  void reset(String categorySlug) {
    state = {...state, categorySlug: CategoryPhotoListInteractionData()};
  }

  void resetAll() {
    state = {};
  }

  void removeCategory(String categorySlug) {
    final newState = Map<String, CategoryPhotoListInteractionData>.from(state);
    newState.remove(categorySlug);
    state = newState;
  }
}

final categoryPhotosInteractionProvider =
    NotifierProvider.autoDispose<
      CategoryPhotosInteractionNotifier,
      Map<String, CategoryPhotoListInteractionData>
    >(CategoryPhotosInteractionNotifier.new);
