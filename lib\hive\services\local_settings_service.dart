import 'package:flutter/material.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:portraitmode/hive/dto/local_settings_data.dart';

class LocalSettingsService {
  static LocalSettingsService? _instance;

  static LocalSettingsService get instance =>
      _instance ??= LocalSettingsService._internal();

  // Private constructor
  LocalSettingsService._internal();

  // Cache boxes to avoid repeated box access
  static Box? _settingsBox;
  static Box? _themeBox;

  // Cache for current settings to avoid frequent Hive reads
  LocalSettingsData? _cachedData;

  // Constants for keys to avoid string allocation
  static const _doneOnboardingKey = 'doneOnboarding';
  static const _themeModeKey = 'themeMode';
  static const _systemTheme = 'system';

  // Pre-computed theme mode map for O(1) lookup instead of linear search
  static const Map<String, ThemeMode> _themeModeMap = {
    'system': ThemeMode.system,
    'light': ThemeMode.light,
    'dark': ThemeMode.dark,
  };

  // Lazy initialization of boxes
  Box get _settingsBoxInstance {
    return _settingsBox ??= Hive.box('settings');
  }

  Box get _themeBoxInstance {
    return _themeBox ??= Hive.box('theme');
  }

  LocalSettingsData _get() {
    // Return cached data if available and still valid
    if (_cachedData != null) {
      return _cachedData!;
    }

    final doneOnboardingFromDb = _settingsBoxInstance.get(
      _doneOnboardingKey,
      defaultValue: 0,
    );

    final themeModeFromDb =
        _themeBoxInstance.get(_themeModeKey, defaultValue: _systemTheme)
            as String;

    // Use map lookup instead of linear search through enum values
    final themeMode = _themeModeMap[themeModeFromDb] ?? ThemeMode.system;

    _cachedData = LocalSettingsData(
      themeMode: themeMode,
      doneOnboarding: doneOnboardingFromDb is int && doneOnboardingFromDb == 1,
    );

    return _cachedData!;
  }

  ThemeMode get _themeMode => _get().themeMode;

  bool get _doneOnboarding => _get().doneOnboarding;

  Future<void> _setThemeMode(ThemeMode value) async {
    await _themeBoxInstance.put(_themeModeKey, value.name);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.themeMode = value;
  }

  Future<void> _setDoneOnboarding(bool value) async {
    await _settingsBoxInstance.put(_doneOnboardingKey, doneOnboarding ? 1 : 0);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.doneOnboarding = value;
  }

  Future<LocalSettingsData> _replace(LocalSettingsData newData) async {
    _invalidateCache();

    await _themeBoxInstance.put(_themeModeKey, newData.themeMode.name);
    await _settingsBoxInstance.put(
      _doneOnboardingKey,
      newData.doneOnboarding ? 1 : 0,
    );

    _cachedData = newData;

    return newData;
  }

  // Explicit cache invalidation method
  void _invalidateCache() {
    _cachedData = null;
  }

  Future<void> _destroy() async {
    await _themeBoxInstance.deleteAll(_themeBoxInstance.keys);
    await _settingsBoxInstance.deleteAll(_settingsBoxInstance.keys);

    _invalidateCache();
  }

  // Cleanup method to be called when app is terminated (just in case needed)
  void _dispose() {
    _cachedData = null;
    _settingsBox = null;
    _themeBox = null;
  }

  // --------------------------------------------------
  // Static convenience methods that delegate to singleton instance
  // --------------------------------------------------

  static LocalSettingsData get() => instance._get();

  static ThemeMode get themeMode => instance._themeMode;

  static bool get doneOnboarding => instance._doneOnboarding;

  static Future<void> setThemeMode(ThemeMode themeMode) =>
      instance._setThemeMode(themeMode);

  static Future<void> setDoneOnboarding(bool doneOnboarding) =>
      instance._setDoneOnboarding(doneOnboarding);

  static Future<LocalSettingsData> replace(LocalSettingsData newData) =>
      instance._replace(newData);

  static void invalidateCache() => instance._invalidateCache();

  static Future<void> destroy() => instance._destroy();

  static void dispose() => instance._dispose();
}
