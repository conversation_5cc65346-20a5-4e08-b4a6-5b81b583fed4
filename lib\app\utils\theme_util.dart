import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';

Brightness getPlatformBrightness(BuildContext context) {
  return MediaQuery.platformBrightnessOf(context);
}

ThemeData getThemeData(BuildContext context, ThemeMode themeMode) {
  final isDarkMode =
      themeMode == ThemeMode.dark ||
      (themeMode == ThemeMode.system &&
          getPlatformBrightness(context) == Brightness.dark);

  final AppColors colors = isDarkMode
      ? AppColorsCache.dark()
      : AppColorsCache.light();

  final Brightness platformBrigtness = getPlatformBrightness(context);

  return ThemeData(
    useMaterial3: true,
    brightness: themeMode == ThemeMode.system
        ? platformBrigtness
        : isDarkMode
        ? Brightness.dark
        : Brightness.light,
    primarySwatch: colors.primarySwatch,
    scaffoldBackgroundColor: colors.scaffoldColor,
    textTheme: GoogleFonts.interTextTheme(
      Theme.of(context).textTheme,
    ).apply(bodyColor: colors.brandColor, displayColor: colors.brandColor),
    appBarTheme: AppBarTheme(
      color: colors.scaffoldColor,
      shadowColor: Colors.transparent,
      elevation: 0.0,
      scrolledUnderElevation: 0,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(color: colors.brandColor),
      iconTheme: IconThemeData(color: colors.brandColor),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      elevation: 0.0,
      type: BottomNavigationBarType.fixed,
      backgroundColor: colors.scaffoldColor,
      showSelectedLabels: false,
      showUnselectedLabels: false,
      selectedItemColor: colors.accentColor,
      unselectedItemColor: colors.brandColor,
    ),
    buttonTheme: ButtonThemeData(
      buttonColor: colors.accentColor,
      height: 50.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        textStyle: TextStyle(
          color: colors.brandColor,
          fontWeight: FontWeight.w400,
        ),
        side: BorderSide(color: colors.baseColor),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      fillColor: colors.scaffoldColor,
      filled: true,
      contentPadding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
      border: OutlineInputBorder(
        borderRadius: const BorderRadius.all(Radius.circular(4.0)),
        borderSide: BorderSide(
          color: colors.baseColor,
          style: BorderStyle.solid,
        ),
      ),
    ),
    visualDensity: VisualDensity.adaptivePlatformDensity,
    dividerTheme: DividerThemeData(
      color: colors.borderColor,
      space: 0.0,
      thickness: 0.8,
    ),
    tabBarTheme: TabBarThemeData(
      dividerHeight: 0.0,
      labelColor: context.isDarkMode ? colors.brandColor : colors.brandColorAlt,
      unselectedLabelColor: colors.primarySwatch[300],
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(width: 2.0, color: colors.brandColor),
        borderRadius: BorderRadius.zero,
      ),
    ),
    bottomSheetTheme: BottomSheetThemeData(
      // backgroundColor: Colors.transparent,
      backgroundColor: colors.scaffoldColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(BottomSheetConfig.borderRadius),
          topRight: Radius.circular(BottomSheetConfig.borderRadius),
        ),
      ),
      // modalBackgroundColor: Colors.transparent,
      modalBackgroundColor: colors.scaffoldColor,
      showDragHandle: false,
      dragHandleColor: colors.greyColor,
      dragHandleSize: const Size(
        BottomSheetConfig.dragHandleWidth,
        BottomSheetConfig.dragHandleHeight,
      ),
    ),
    snackBarTheme: SnackBarThemeData(
      backgroundColor: isDarkMode
          ? AppColorsCache.dark().baseColorAlt
          : AppColorsCache.light().primarySwatch[900],
      contentTextStyle: TextStyle(
        color: isDarkMode
            ? AppColorsCache.dark().brandColor
            : AppColorsCache.light().scaffoldColor,
      ),
    ),
  );
}
